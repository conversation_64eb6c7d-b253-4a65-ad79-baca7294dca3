"use client";
import { useState, useEffect } from 'react';
import Image from 'next/image';

interface ImageWithFallbackProps {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
  loading?: 'lazy' | 'eager';
  onError?: () => void;
  style?: React.CSSProperties;
  width?: number;
  height?: number;
  fill?: boolean;
  priority?: boolean;
  sizes?: string;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
}

const DEFAULT_FALLBACKS = [
  '/assets/img/placeholders/project-placeholder.svg',
  '/assets/img/placeholders/project-placeholder-square.svg',
  '/placeholder.svg',
  '/placeholder.jpg'
];

export default function ImageWithFallback({
  src,
  alt,
  className = '',
  fallbackSrc,
  loading = 'lazy',
  onError,
  style,
  width,
  height,
  fill = false,
  priority = false,
  sizes,
  quality = 75,
  placeholder = 'empty',
  blurDataURL
}: ImageWithFallbackProps) {
  const [currentSrc, setCurrentSrc] = useState(src);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [fallbackIndex, setFallbackIndex] = useState(0);

  // Reset state when src changes
  useEffect(() => {
    setCurrentSrc(src);
    setHasError(false);
    setIsLoading(true);
    setFallbackIndex(0);
  }, [src]);

  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
    onError?.();

    // Try custom fallback first
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc);
      return;
    }

    // Try default fallbacks
    if (fallbackIndex < DEFAULT_FALLBACKS.length) {
      setCurrentSrc(DEFAULT_FALLBACKS[fallbackIndex]);
      setFallbackIndex(prev => prev + 1);
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const isPlaceholder = hasError || DEFAULT_FALLBACKS.includes(currentSrc) || currentSrc === fallbackSrc;

  // Common props for both Next.js Image and regular img
  const commonProps = {
    src: currentSrc,
    alt,
    className: `image-with-fallback ${isPlaceholder ? 'placeholder-image' : ''} ${isLoading ? 'loading' : ''} ${className}`,
    onError: handleError,
    onLoad: handleLoad,
    style
  };

  // Use Next.js Image component when dimensions are provided or fill is true
  if ((width && height) || fill) {
    return (
      <div className={`image-container ${className}`} style={style}>
        {isLoading && (
          <div className="image-loading-placeholder">
            <div className="loading-spinner"></div>
          </div>
        )}
        <Image
          {...commonProps}
          width={width}
          height={height}
          fill={fill}
          priority={priority}
          sizes={sizes}
          quality={quality}
          placeholder={placeholder}
          blurDataURL={blurDataURL}
          loading={loading}
        />
        
        <style jsx>{`
          .image-container {
            position: relative;
            overflow: hidden;
          }
          
          .image-with-fallback {
            transition: opacity 0.3s ease, transform 0.3s ease;
          }
          
          .image-with-fallback.loading {
            opacity: 0;
          }
          
          .image-with-fallback.placeholder-image {
            object-fit: contain;
            background-color: #f8f9fa;
            padding: 1rem;
          }
          
          .image-loading-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
          }
          
          .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #e9ecef;
            border-top: 3px solid #6c757d;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
          
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  // Use regular img tag for responsive images without fixed dimensions
  return (
    <div className={`image-container ${className}`} style={style}>
      {isLoading && (
        <div className="image-loading-placeholder">
          <div className="loading-spinner"></div>
        </div>
      )}
      <img
        {...commonProps}
        loading={loading}
      />
      
      <style jsx>{`
        .image-container {
          position: relative;
          overflow: hidden;
        }
        
        .image-with-fallback {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: opacity 0.3s ease, transform 0.3s ease;
        }
        
        .image-with-fallback.loading {
          opacity: 0;
        }
        
        .image-with-fallback.placeholder-image {
          object-fit: contain;
          background-color: #f8f9fa;
          padding: 1rem;
        }
        
        .image-loading-placeholder {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: #f8f9fa;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1;
        }
        
        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid #e9ecef;
          border-top: 3px solid #6c757d;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
