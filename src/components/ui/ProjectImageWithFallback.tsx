"use client";
import { useState } from 'react';
import Image from 'next/image';
import { resolveImagePath, getFallbackImage, getImageFallbacks } from '@/utils/imageUtils';

interface ProjectImageWithFallbackProps {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
  loading?: 'lazy' | 'eager';
  onError?: () => void;
  style?: React.CSSProperties;
}

export default function ProjectImageWithFallback({
  src,
  alt,
  className = '',
  fallbackSrc,
  loading = 'lazy',
  onError,
  style
}: ProjectImageWithFallbackProps) {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [currentFallbackIndex, setCurrentFallbackIndex] = useState(0);

  // Get fallback options
  const fallbackOptions = getImageFallbacks(src, 'project');
  const defaultFallback = fallbackSrc || getFallbackImage('project');

  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
    onError?.();

    // Try next fallback option
    if (currentFallbackIndex < fallbackOptions.length - 1) {
      setCurrentFallbackIndex(prev => prev + 1);
      setHasError(false);
      setIsLoading(true);
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
  };

  // Determine current image source
  let imageSrc: string;
  if (hasError && currentFallbackIndex < fallbackOptions.length) {
    imageSrc = fallbackOptions[currentFallbackIndex];
  } else if (hasError) {
    imageSrc = defaultFallback;
  } else {
    imageSrc = resolveImagePath(src);
  }

  const isPlaceholder = hasError || imageSrc === defaultFallback || fallbackOptions.includes(imageSrc);

  return (
    <div className={`project-image-container ${className}`} style={style}>
      {isLoading && (
        <div className="image-loading-placeholder">
          <div className="loading-spinner"></div>
        </div>
      )}
      <img
        src={imageSrc}
        alt={alt}
        className={`project-image ${isPlaceholder ? 'placeholder-image' : ''} ${isLoading ? 'loading' : ''}`}
        onError={handleError}
        onLoad={handleLoad}
        loading={loading}
      />
      
      <style jsx>{`
        .project-image-container {
          position: relative;
          overflow: hidden;
        }
        
        .project-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: opacity 0.3s ease, transform 0.3s ease;
        }
        
        .project-image.loading {
          opacity: 0;
        }
        
        .project-image.placeholder-image {
          object-fit: contain;
          background-color: #f8f9fa;
          padding: 1rem;
        }
        
        .image-loading-placeholder {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: #f8f9fa;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1;
        }
        
        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid #e9ecef;
          border-top: 3px solid #6c757d;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
          .project-image.placeholder-image {
            padding: 0.5rem;
          }
          
          .loading-spinner {
            width: 30px;
            height: 30px;
            border-width: 2px;
          }
        }
      `}</style>
    </div>
  );
}
