#!/usr/bin/env node

/**
 * Image validation script
 * Checks if image files referenced in data files actually exist
 */

const fs = require('fs');
const path = require('path');

// Directories to check
const DATA_DIRS = [
  'src/data',
  'src/data/products',
  'src/data/projects',
  'src/data/sections'
];

const PUBLIC_DIR = 'public';

// Image extensions to validate
const IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'];

/**
 * Check if a file exists
 */
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

/**
 * Extract image URLs from JSON content
 */
function extractImageUrls(content) {
  const urls = [];
  const urlRegex = /"(?:url|src|image)"\s*:\s*"([^"]+)"/g;
  let match;
  
  while ((match = urlRegex.exec(content)) !== null) {
    const url = match[1];
    if (IMAGE_EXTENSIONS.some(ext => url.toLowerCase().includes(ext))) {
      urls.push(url);
    }
  }
  
  return urls;
}

/**
 * Convert URL to file path
 */
function urlToFilePath(url) {
  // Remove leading slash and convert to file path
  const cleanUrl = url.startsWith('/') ? url.substring(1) : url;
  return path.join(PUBLIC_DIR, cleanUrl);
}

/**
 * Validate images in a JSON file
 */
function validateJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const imageUrls = extractImageUrls(content);
    const results = [];
    
    for (const url of imageUrls) {
      const fullPath = urlToFilePath(url);
      const exists = fileExists(fullPath);
      
      results.push({
        url,
        path: fullPath,
        exists,
        file: filePath
      });
    }
    
    return results;
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return [];
  }
}

/**
 * Get all JSON files in a directory
 */
function getJsonFiles(dir) {
  const files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files.push(...getJsonFiles(fullPath));
      } else if (item.endsWith('.json')) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

/**
 * Main validation function
 */
function validateImages() {
  console.log('🔍 Validating image references...\n');
  
  let totalImages = 0;
  let missingImages = 0;
  const missingFiles = [];
  
  // Get all JSON files
  const allJsonFiles = [];
  for (const dir of DATA_DIRS) {
    if (fs.existsSync(dir)) {
      allJsonFiles.push(...getJsonFiles(dir));
    }
  }
  
  console.log(`Found ${allJsonFiles.length} JSON files to check\n`);
  
  // Validate each file
  for (const jsonFile of allJsonFiles) {
    const results = validateJsonFile(jsonFile);
    
    if (results.length > 0) {
      console.log(`📄 ${jsonFile}:`);
      
      for (const result of results) {
        totalImages++;
        
        if (result.exists) {
          console.log(`  ✅ ${result.url}`);
        } else {
          console.log(`  ❌ ${result.url} (missing: ${result.path})`);
          missingImages++;
          missingFiles.push(result);
        }
      }
      
      console.log('');
    }
  }
  
  // Summary
  console.log('📊 Summary:');
  console.log(`  Total images found: ${totalImages}`);
  console.log(`  Missing images: ${missingImages}`);
  console.log(`  Success rate: ${totalImages > 0 ? Math.round(((totalImages - missingImages) / totalImages) * 100) : 100}%`);
  
  if (missingFiles.length > 0) {
    console.log('\n🚨 Missing files:');
    for (const missing of missingFiles) {
      console.log(`  - ${missing.url} (in ${missing.file})`);
    }
    
    console.log('\n💡 Suggestions:');
    console.log('  1. Check if the image files exist in the correct directories');
    console.log('  2. Update the JSON files to use correct image paths');
    console.log('  3. Copy missing images from mitc_images to the correct locations');
  } else {
    console.log('\n🎉 All image references are valid!');
  }
  
  return missingImages === 0;
}

// Run validation
if (require.main === module) {
  const success = validateImages();
  process.exit(success ? 0 : 1);
}

module.exports = { validateImages };
