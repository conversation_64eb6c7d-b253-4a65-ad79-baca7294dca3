#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to fix image paths in JSON files
 * Converts /img/ paths to /assets/img/ paths
 */

const fs = require('fs');
const path = require('path');

// Directories to process
const DATA_DIRS = [
  'src/data',
  'src/data/products',
  'src/data/projects',
  'src/data/sections'
];

/**
 * Get all JSON files in a directory recursively
 */
function getJsonFiles(dir) {
  const files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files.push(...getJsonFiles(fullPath));
      } else if (item.endsWith('.json')) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

/**
 * Fix image paths in JSON content
 */
function fixImagePaths(content) {
  // Replace /img/ with /assets/img/ but not if it's already /assets/img/
  return content.replace(/(?<!\/assets)\/img\//g, '/assets/img/');
}

/**
 * Process a single JSON file
 */
function processJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixImagePaths(content);
    
    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Main function
 */
function fixAllImagePaths() {
  console.log('🔧 Fixing image paths in JSON files...\n');
  
  let totalFiles = 0;
  let fixedFiles = 0;
  
  // Get all JSON files
  const allJsonFiles = [];
  for (const dir of DATA_DIRS) {
    if (fs.existsSync(dir)) {
      allJsonFiles.push(...getJsonFiles(dir));
    }
  }
  
  console.log(`Found ${allJsonFiles.length} JSON files to process\n`);
  
  // Process each file
  for (const jsonFile of allJsonFiles) {
    totalFiles++;
    if (processJsonFile(jsonFile)) {
      fixedFiles++;
    }
  }
  
  // Summary
  console.log('\n📊 Summary:');
  console.log(`  Total files processed: ${totalFiles}`);
  console.log(`  Files fixed: ${fixedFiles}`);
  console.log(`  Files unchanged: ${totalFiles - fixedFiles}`);
  
  if (fixedFiles > 0) {
    console.log('\n🎉 Image paths have been fixed!');
  } else {
    console.log('\n✨ All image paths were already correct!');
  }
  
  return fixedFiles;
}

// Run the script
if (require.main === module) {
  const fixedCount = fixAllImagePaths();
  process.exit(0);
}

module.exports = { fixAllImagePaths };
